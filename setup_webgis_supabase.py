"""
Setup database untuk ScapeGIS Web GIS menggunakan Supabase client
Membuat tabel maps, geo_features, saved_maps, dan comments
"""
import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

def setup_webgis_tables():
    """Setup tabel untuk ScapeGIS Web GIS menggunakan Supabase"""
    
    try:
        # Create Supabase client with service role key
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        print("🔗 Connected to Supabase")
        
        # SQL untuk membuat semua tabel
        sql_commands = [
            # 1. Create maps table
            """
            CREATE TABLE IF NOT EXISTS public.maps (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                base_map_type VARCHAR(20) DEFAULT 'street',
                center_lat FLOAT DEFAULT 0.0,
                center_lng FLOAT DEFAULT 0.0,
                zoom_level INTEGER DEFAULT 2,
                settings JSONB DEFAULT '{}',
                bounds JSONB,
                is_public BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                view_count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                feature_count INTEGER DEFAULT 0,
                last_accessed TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """,
            
            # 2. Create geo_features table
            """
            CREATE TABLE IF NOT EXISTS public.geo_features (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                feature_type VARCHAR(20) NOT NULL,
                geometry JSONB NOT NULL,
                properties JSONB DEFAULT '{}',
                image_url TEXT,
                is_visible BOOLEAN DEFAULT TRUE,
                z_index INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """,
            
            # 3. Create saved_maps table
            """
            CREATE TABLE IF NOT EXISTS public.saved_maps (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
                map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
                saved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(user_id, map_id)
            );
            """,
            
            # 4. Create comments table
            """
            CREATE TABLE IF NOT EXISTS public.comments (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
                map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
                content TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            """,
            
            # 5. Create indexes
            "CREATE INDEX IF NOT EXISTS idx_maps_user_id ON public.maps(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_maps_is_public ON public.maps(is_public);",
            "CREATE INDEX IF NOT EXISTS idx_maps_created_at ON public.maps(created_at DESC);",
            "CREATE INDEX IF NOT EXISTS idx_geo_features_map_id ON public.geo_features(map_id);",
            "CREATE INDEX IF NOT EXISTS idx_saved_maps_user_id ON public.saved_maps(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_comments_map_id ON public.comments(map_id);",
        ]
        
        # Execute SQL commands
        print("📝 Creating tables...")
        for i, sql in enumerate(sql_commands, 1):
            try:
                result = supabase.rpc('exec_sql', {'sql': sql.strip()}).execute()
                print(f"   ✅ Command {i}/{len(sql_commands)} executed")
            except Exception as e:
                print(f"   ⚠️ Command {i} failed (might already exist): {str(e)[:100]}...")
        
        print("\n✅ Database setup completed!")
        print("\n📊 Created tables:")
        print("   - maps (user-created maps)")
        print("   - geo_features (markers, polygons, etc.)")
        print("   - saved_maps (bookmarked maps)")
        print("   - comments (map comments)")
        print("\n🔍 Indexes created for better performance")
        
        # Test table creation by checking if tables exist
        print("\n🧪 Testing table creation...")
        try:
            # Try to query each table (should return empty result if table exists)
            tables_to_test = ['maps', 'geo_features', 'saved_maps', 'comments']
            for table in tables_to_test:
                result = supabase.table(table).select('id').limit(1).execute()
                print(f"   ✅ Table '{table}' exists and accessible")
        except Exception as e:
            print(f"   ⚠️ Table test failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False


def create_rls_policies():
    """Create Row Level Security policies"""
    try:
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)
        
        print("🔒 Setting up Row Level Security policies...")
        
        # RLS policies SQL
        rls_commands = [
            # Enable RLS
            "ALTER TABLE public.maps ENABLE ROW LEVEL SECURITY;",
            "ALTER TABLE public.geo_features ENABLE ROW LEVEL SECURITY;",
            "ALTER TABLE public.saved_maps ENABLE ROW LEVEL SECURITY;",
            "ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;",
            
            # Maps policies
            """
            DROP POLICY IF EXISTS "Users can view their own maps" ON public.maps;
            CREATE POLICY "Users can view their own maps" ON public.maps
                FOR SELECT USING (auth.uid() = user_id);
            """,
            
            """
            DROP POLICY IF EXISTS "Users can view public maps" ON public.maps;
            CREATE POLICY "Users can view public maps" ON public.maps
                FOR SELECT USING (is_public = true);
            """,
            
            """
            DROP POLICY IF EXISTS "Users can insert their own maps" ON public.maps;
            CREATE POLICY "Users can insert their own maps" ON public.maps
                FOR INSERT WITH CHECK (auth.uid() = user_id);
            """,
            
            """
            DROP POLICY IF EXISTS "Users can update their own maps" ON public.maps;
            CREATE POLICY "Users can update their own maps" ON public.maps
                FOR UPDATE USING (auth.uid() = user_id);
            """,
            
            """
            DROP POLICY IF EXISTS "Users can delete their own maps" ON public.maps;
            CREATE POLICY "Users can delete their own maps" ON public.maps
                FOR DELETE USING (auth.uid() = user_id);
            """,
        ]
        
        for i, sql in enumerate(rls_commands, 1):
            try:
                result = supabase.rpc('exec_sql', {'sql': sql.strip()}).execute()
                print(f"   ✅ RLS Policy {i}/{len(rls_commands)} created")
            except Exception as e:
                print(f"   ⚠️ RLS Policy {i} failed: {str(e)[:100]}...")
        
        print("✅ RLS policies setup completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error setting up RLS policies: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Setting up ScapeGIS Web GIS database with Supabase...")
    
    if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
        print("❌ Missing Supabase configuration. Please check your .env file.")
        exit(1)
    
    # Setup tables
    if setup_webgis_tables():
        print("\n🎉 Database setup successful!")
        
        # Optionally setup RLS policies (commented out for now)
        # print("\n🔒 Setting up security policies...")
        # create_rls_policies()
        
    else:
        print("\n❌ Database setup failed!")
        exit(1)
