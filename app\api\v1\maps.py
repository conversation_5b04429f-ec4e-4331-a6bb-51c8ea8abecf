"""
Maps API endpoints for ScapeGIS Web GIS
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from supabase import Client
from app.core.database import get_supabase
from app.core.auth import get_current_user
from app.schemas.map import (
    CreateMapRequest, UpdateMapRequest, MapResponse, MapPublicResponse,
    CreateGeoFeatureRequest, UpdateGeoFeatureRequest, GeoFeatureResponse,
    CreateCommentRequest, CommentResponse, SavedMapResponse,
    MapListResponse, PublicMapListResponse
)
import logging
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/maps", tags=["maps"])


@router.post("/", response_model=MapResponse)
async def create_map(
    map_data: CreateMapRequest,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Create a new map"""
    try:
        # Prepare map data
        new_map = {
            "id": str(uuid.uuid4()),
            "user_id": current_user["id"],
            "title": map_data.title,
            "description": map_data.description,
            "base_map_type": map_data.base_map_type,
            "center_lat": map_data.center.lat if map_data.center else 0.0,
            "center_lng": map_data.center.lng if map_data.center else 0.0,
            "zoom_level": map_data.zoom_level,
            "settings": map_data.settings,
            "bounds": map_data.bounds,
            "is_public": map_data.is_public,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        # Insert into Supabase
        result = supabase.table("maps").insert(new_map).execute()
        
        if result.data:
            created_map = result.data[0]
            return MapResponse(
                id=created_map["id"],
                user_id=created_map["user_id"],
                title=created_map["title"],
                description=created_map.get("description"),
                base_map_type=created_map["base_map_type"],
                center={
                    "lat": created_map["center_lat"],
                    "lng": created_map["center_lng"]
                },
                zoom_level=created_map["zoom_level"],
                settings=created_map.get("settings", {}),
                bounds=created_map.get("bounds"),
                is_public=created_map["is_public"],
                is_featured=created_map.get("is_featured", False),
                view_count=created_map.get("view_count", 0),
                like_count=created_map.get("like_count", 0),
                feature_count=created_map.get("feature_count", 0),
                last_accessed=created_map.get("last_accessed"),
                created_at=created_map.get("created_at"),
                updated_at=created_map.get("updated_at")
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create map"
            )
            
    except Exception as e:
        logger.error(f"Error creating map: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create map: {str(e)}"
        )


@router.get("/", response_model=MapListResponse)
async def get_user_maps(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Get user's maps with pagination"""
    try:
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Get user's maps
        result = supabase.table("maps")\
            .select("*")\
            .eq("user_id", current_user["id"])\
            .order("created_at", desc=True)\
            .range(offset, offset + per_page - 1)\
            .execute()
        
        # Get total count
        count_result = supabase.table("maps")\
            .select("id", count="exact")\
            .eq("user_id", current_user["id"])\
            .execute()
        
        total = count_result.count if count_result.count else 0
        total_pages = (total + per_page - 1) // per_page
        
        maps = []
        if result.data:
            for map_data in result.data:
                maps.append(MapResponse(
                    id=map_data["id"],
                    user_id=map_data["user_id"],
                    title=map_data["title"],
                    description=map_data.get("description"),
                    base_map_type=map_data["base_map_type"],
                    center={
                        "lat": map_data["center_lat"],
                        "lng": map_data["center_lng"]
                    },
                    zoom_level=map_data["zoom_level"],
                    settings=map_data.get("settings", {}),
                    bounds=map_data.get("bounds"),
                    is_public=map_data["is_public"],
                    is_featured=map_data.get("is_featured", False),
                    view_count=map_data.get("view_count", 0),
                    like_count=map_data.get("like_count", 0),
                    feature_count=map_data.get("feature_count", 0),
                    last_accessed=map_data.get("last_accessed"),
                    created_at=map_data.get("created_at"),
                    updated_at=map_data.get("updated_at")
                ))
        
        return MapListResponse(
            maps=maps,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"Error getting user maps: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get maps: {str(e)}"
        )


@router.get("/public", response_model=PublicMapListResponse)
async def get_public_maps(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    featured: Optional[bool] = Query(None, description="Filter featured maps"),
    supabase: Client = Depends(get_supabase)
):
    """Get public maps with pagination"""
    try:
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Build query
        query = supabase.table("maps").select("*").eq("is_public", True)
        
        if featured is not None:
            query = query.eq("is_featured", featured)
        
        # Execute query with pagination
        result = query.order("created_at", desc=True)\
            .range(offset, offset + per_page - 1)\
            .execute()
        
        # Get total count
        count_query = supabase.table("maps").select("id", count="exact").eq("is_public", True)
        if featured is not None:
            count_query = count_query.eq("is_featured", featured)
        
        count_result = count_query.execute()
        total = count_result.count if count_result.count else 0
        total_pages = (total + per_page - 1) // per_page
        
        maps = []
        if result.data:
            for map_data in result.data:
                maps.append(MapPublicResponse(
                    id=map_data["id"],
                    title=map_data["title"],
                    description=map_data.get("description"),
                    base_map_type=map_data["base_map_type"],
                    center={
                        "lat": map_data["center_lat"],
                        "lng": map_data["center_lng"]
                    },
                    zoom_level=map_data["zoom_level"],
                    bounds=map_data.get("bounds"),
                    is_featured=map_data.get("is_featured", False),
                    view_count=map_data.get("view_count", 0),
                    like_count=map_data.get("like_count", 0),
                    feature_count=map_data.get("feature_count", 0),
                    created_at=map_data.get("created_at")
                ))
        
        return PublicMapListResponse(
            maps=maps,
            total=total,
            page=page,
            per_page=per_page,
            total_pages=total_pages
        )
        
    except Exception as e:
        logger.error(f"Error getting public maps: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get public maps: {str(e)}"
        )


@router.get("/{map_id}", response_model=MapResponse)
async def get_map(
    map_id: str,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Get a specific map by ID"""
    try:
        # Get map
        result = supabase.table("maps")\
            .select("*")\
            .eq("id", map_id)\
            .execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Map not found"
            )
        
        map_data = result.data[0]
        
        # Check if user has access (owner or public map)
        if map_data["user_id"] != current_user["id"] and not map_data["is_public"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update view count if not owner
        if map_data["user_id"] != current_user["id"]:
            supabase.table("maps")\
                .update({"view_count": map_data.get("view_count", 0) + 1})\
                .eq("id", map_id)\
                .execute()
            map_data["view_count"] = map_data.get("view_count", 0) + 1
        
        return MapResponse(
            id=map_data["id"],
            user_id=map_data["user_id"],
            title=map_data["title"],
            description=map_data.get("description"),
            base_map_type=map_data["base_map_type"],
            center={
                "lat": map_data["center_lat"],
                "lng": map_data["center_lng"]
            },
            zoom_level=map_data["zoom_level"],
            settings=map_data.get("settings", {}),
            bounds=map_data.get("bounds"),
            is_public=map_data["is_public"],
            is_featured=map_data.get("is_featured", False),
            view_count=map_data.get("view_count", 0),
            like_count=map_data.get("like_count", 0),
            feature_count=map_data.get("feature_count", 0),
            last_accessed=map_data.get("last_accessed"),
            created_at=map_data.get("created_at"),
            updated_at=map_data.get("updated_at")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting map: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get map: {str(e)}"
        )


@router.put("/{map_id}", response_model=MapResponse)
async def update_map(
    map_id: str,
    map_data: UpdateMapRequest,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Update a map"""
    try:
        # Check if map exists and user owns it
        result = supabase.table("maps")\
            .select("*")\
            .eq("id", map_id)\
            .eq("user_id", current_user["id"])\
            .execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Map not found or access denied"
            )

        # Prepare update data
        update_data = {"updated_at": datetime.utcnow().isoformat()}

        if map_data.title is not None:
            update_data["title"] = map_data.title
        if map_data.description is not None:
            update_data["description"] = map_data.description
        if map_data.base_map_type is not None:
            update_data["base_map_type"] = map_data.base_map_type
        if map_data.center is not None:
            update_data["center_lat"] = map_data.center.lat
            update_data["center_lng"] = map_data.center.lng
        if map_data.zoom_level is not None:
            update_data["zoom_level"] = map_data.zoom_level
        if map_data.bounds is not None:
            update_data["bounds"] = map_data.bounds
        if map_data.is_public is not None:
            update_data["is_public"] = map_data.is_public
        if map_data.settings is not None:
            update_data["settings"] = map_data.settings

        # Update map
        result = supabase.table("maps")\
            .update(update_data)\
            .eq("id", map_id)\
            .execute()

        if result.data:
            updated_map = result.data[0]
            return MapResponse(
                id=updated_map["id"],
                user_id=updated_map["user_id"],
                title=updated_map["title"],
                description=updated_map.get("description"),
                base_map_type=updated_map["base_map_type"],
                center={
                    "lat": updated_map["center_lat"],
                    "lng": updated_map["center_lng"]
                },
                zoom_level=updated_map["zoom_level"],
                settings=updated_map.get("settings", {}),
                bounds=updated_map.get("bounds"),
                is_public=updated_map["is_public"],
                is_featured=updated_map.get("is_featured", False),
                view_count=updated_map.get("view_count", 0),
                like_count=updated_map.get("like_count", 0),
                feature_count=updated_map.get("feature_count", 0),
                last_accessed=updated_map.get("last_accessed"),
                created_at=updated_map.get("created_at"),
                updated_at=updated_map.get("updated_at")
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update map"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating map: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update map: {str(e)}"
        )


@router.delete("/{map_id}")
async def delete_map(
    map_id: str,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Delete a map"""
    try:
        # Check if map exists and user owns it
        result = supabase.table("maps")\
            .select("id")\
            .eq("id", map_id)\
            .eq("user_id", current_user["id"])\
            .execute()

        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Map not found or access denied"
            )

        # Delete map (cascade will delete related features, comments, etc.)
        result = supabase.table("maps")\
            .delete()\
            .eq("id", map_id)\
            .execute()

        return {"message": "Map deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting map: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete map: {str(e)}"
        )
