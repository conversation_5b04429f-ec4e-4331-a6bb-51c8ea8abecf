# 🚀 Frontend OAuth Google Implementation Guide

## 📋 **Overview**
Panduan lengkap untuk mengimplementasikan OAuth Google di React frontend yang terintegrasi dengan FastAPI backend ScapeGIS.

## 🔄 **Complete OAuth Flow**

### **1. User Initiates Login**
```javascript
// LoginPage.jsx atau AuthComponent.jsx
const handleGoogleLogin = () => {
  // Redirect ke backend OAuth endpoint
  window.location.href = 'http://localhost:8001/api/v1/auth/oauth/google';
};

// Button component
<button 
  onClick={handleGoogleLogin}
  className="google-login-btn"
>
  <img src="/google-icon.svg" alt="Google" />
  Login with Google
</button>
```

### **2. Backend Processing**
Backend akan:
- Redirect ke Google OAuth
- Handle Google callback
- Process user data
- Generate JWT tokens
- Redirect kembali ke frontend

### **3. Frontend Callback Handler**
```javascript
// pages/auth/callback.jsx atau AuthCallback.jsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
// atau import { useNavigate, useSearchParams } from 'react-router-dom';

const AuthCallback = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        
        // Check for OAuth success
        const oauthSuccess = urlParams.get('oauth_success');
        const accessToken = urlParams.get('access_token');
        const refreshToken = urlParams.get('refresh_token');
        const userId = urlParams.get('user_id');
        const email = urlParams.get('email');
        const name = urlParams.get('name');
        const avatarUrl = urlParams.get('avatar_url');
        const provider = urlParams.get('provider');

        if (oauthSuccess === 'true' && accessToken) {
          // Store tokens securely
          localStorage.setItem('access_token', accessToken);
          localStorage.setItem('refresh_token', refreshToken);
          
          // Store user info
          const userInfo = {
            id: userId,
            email: email,
            name: name,
            avatar_url: avatarUrl,
            provider: provider
          };
          localStorage.setItem('user_info', JSON.stringify(userInfo));

          // Update auth context/state
          // setUser(userInfo);
          // setIsAuthenticated(true);

          // Redirect to dashboard
          router.push('/dashboard');
          
        } else {
          throw new Error('OAuth authentication failed');
        }
        
      } catch (err) {
        console.error('OAuth callback error:', err);
        setError(err.message);
        // Redirect to login with error
        router.push('/login?error=oauth_failed');
      } finally {
        setLoading(false);
      }
    };

    handleCallback();
  }, [router]);

  if (loading) {
    return (
      <div className="auth-callback-loading">
        <div className="spinner"></div>
        <p>Completing authentication...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="auth-callback-error">
        <h2>Authentication Error</h2>
        <p>{error}</p>
        <button onClick={() => router.push('/login')}>
          Back to Login
        </button>
      </div>
    );
  }

  return null;
};

export default AuthCallback;
```

### **4. Error Handler Page**
```javascript
// pages/auth/error.jsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

const AuthError = () => {
  const router = useRouter();
  const [errorInfo, setErrorInfo] = useState({});

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    setErrorInfo({
      error: urlParams.get('error'),
      details: urlParams.get('details')
    });
  }, []);

  const getErrorMessage = (error) => {
    switch (error) {
      case 'callback_failed':
        return 'OAuth callback processing failed. Please try again.';
      case 'oauth_init_failed':
        return 'Failed to initiate OAuth. Please check configuration.';
      case 'no_code':
        return 'No authorization code received from Google.';
      default:
        return 'An unknown error occurred during authentication.';
    }
  };

  return (
    <div className="auth-error-page">
      <div className="error-container">
        <h1>Authentication Error</h1>
        <p>{getErrorMessage(errorInfo.error)}</p>
        {errorInfo.details && (
          <details>
            <summary>Error Details</summary>
            <pre>{errorInfo.details}</pre>
          </details>
        )}
        <div className="error-actions">
          <button onClick={() => router.push('/login')}>
            Try Again
          </button>
          <button onClick={() => router.push('/')}>
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthError;
```

### **5. Auth Context/Hook**
```javascript
// contexts/AuthContext.jsx atau hooks/useAuth.js
import { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing auth on app load
    const checkAuth = () => {
      try {
        const token = localStorage.getItem('access_token');
        const userInfo = localStorage.getItem('user_info');
        
        if (token && userInfo) {
          setUser(JSON.parse(userInfo));
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Auth check error:', error);
        // Clear invalid data
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const logout = () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_info');
    setUser(null);
    setIsAuthenticated(false);
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    logout,
    setUser,
    setIsAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
```

## 🛠 **Required Routes**

### **Next.js App Router:**
```
pages/
├── auth/
│   ├── callback.jsx    # Handle OAuth success
│   └── error.jsx       # Handle OAuth errors
├── login.jsx           # Login page with Google button
└── dashboard.jsx       # Protected page after login
```

### **React Router:**
```javascript
// App.jsx atau routes configuration
import { BrowserRouter, Routes, Route } from 'react-router-dom';

function App() {
  return (
    <AuthProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/auth/callback" element={<AuthCallback />} />
          <Route path="/auth/error" element={<AuthError />} />
          <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
        </Routes>
      </BrowserRouter>
    </AuthProvider>
  );
}
```

## 🔒 **Security Considerations**

1. **Token Storage**: Gunakan httpOnly cookies untuk production
2. **HTTPS**: Pastikan menggunakan HTTPS di production
3. **Token Validation**: Validasi token di setiap API call
4. **Refresh Token**: Implement token refresh mechanism

## 🧪 **Testing Flow**

1. Klik "Login with Google" button
2. Redirect ke Google OAuth
3. Login dengan akun Google
4. Consent untuk aplikasi
5. Redirect ke `/auth/callback` dengan tokens
6. Tokens disimpan dan user redirect ke dashboard

## 🐛 **Error yang Sudah Diperbaiki**

Error `'dict' object has no attribute 'email'` sudah diperbaiki di backend dengan menggunakan `user_profile.get('email')` instead of `user_profile.email`.
