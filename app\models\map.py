"""
Map Model for ScapeGIS Web GIS
"""
from sqlalchemy import Column, String, Text, Integer, Boolean, DateTime, ForeignKey, Float
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from typing import Optional, List, Dict, Any
from app.core.database import Base


class Map(Base):
    """Map model for user-created maps in ScapeGIS"""
    __tablename__ = "maps"
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id", ondelete="CASCADE"), nullable=False)
    title = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Map configuration
    base_map_type = Column(String(20), default='street')  # 'street', 'satellite', 'dark', 'terrain'
    center_lat = Column(Float, default=0.0)
    center_lng = Column(Float, default=0.0)
    zoom_level = Column(Integer, default=2)
    
    # Map settings and bounds
    settings = Column(JSONB, default={})  # Additional map settings
    bounds = Column(JSONB)  # Map bounds [[sw_lng, sw_lat], [ne_lng, ne_lat]]
    
    # Visibility and sharing
    is_public = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)  # For featured maps
    
    # Statistics
    view_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    feature_count = Column(Integer, default=0)  # Number of markers/features
    
    # Timestamps
    last_accessed = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    features = relationship("GeoFeature", back_populates="map", cascade="all, delete-orphan")
    saved_by = relationship("SavedMap", back_populates="map", cascade="all, delete-orphan")
    comments = relationship("Comment", back_populates="map", cascade="all, delete-orphan")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "title": self.title,
            "description": self.description,
            "base_map_type": self.base_map_type,
            "center": {
                "lat": self.center_lat,
                "lng": self.center_lng
            },
            "zoom_level": self.zoom_level,
            "settings": self.settings or {},
            "bounds": self.bounds,
            "is_public": self.is_public,
            "is_featured": self.is_featured,
            "view_count": self.view_count,
            "like_count": self.like_count,
            "feature_count": self.feature_count,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
    
    def to_public_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for public API response (limited info)"""
        return {
            "id": str(self.id),
            "title": self.title,
            "description": self.description,
            "base_map_type": self.base_map_type,
            "center": {
                "lat": self.center_lat,
                "lng": self.center_lng
            },
            "zoom_level": self.zoom_level,
            "bounds": self.bounds,
            "is_featured": self.is_featured,
            "view_count": self.view_count,
            "like_count": self.like_count,
            "feature_count": self.feature_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class GeoFeature(Base):
    """Geo Feature model for markers, polygons, and other geospatial elements"""
    __tablename__ = "geo_features"
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    map_id = Column(UUID(as_uuid=True), ForeignKey("maps.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Feature type and geometry
    feature_type = Column(String(20), nullable=False)  # 'marker', 'polygon', 'line', 'circle'
    geometry = Column(JSONB, nullable=False)  # GeoJSON geometry
    
    # Feature properties
    properties = Column(JSONB, default={})  # Additional properties (color, icon, etc.)
    
    # Media
    image_url = Column(Text)  # Optional image for the feature
    
    # Visibility
    is_visible = Column(Boolean, default=True)
    z_index = Column(Integer, default=0)  # Layer order
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    map = relationship("Map", back_populates="features")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "id": str(self.id),
            "map_id": str(self.map_id),
            "name": self.name,
            "description": self.description,
            "feature_type": self.feature_type,
            "geometry": self.geometry,
            "properties": self.properties or {},
            "image_url": self.image_url,
            "is_visible": self.is_visible,
            "z_index": self.z_index,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class SavedMap(Base):
    """Saved Map model for users to bookmark/save public maps"""
    __tablename__ = "saved_maps"
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id", ondelete="CASCADE"), nullable=False)
    map_id = Column(UUID(as_uuid=True), ForeignKey("maps.id", ondelete="CASCADE"), nullable=False)
    
    # Timestamps
    saved_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    map = relationship("Map", back_populates="saved_by")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "map_id": str(self.map_id),
            "saved_at": self.saved_at.isoformat() if self.saved_at else None,
        }


class Comment(Base):
    """Comment model for comments on public maps"""
    __tablename__ = "comments"
    
    id = Column(UUID(as_uuid=True), primary_key=True, server_default=func.gen_random_uuid())
    user_id = Column(UUID(as_uuid=True), ForeignKey("user_profiles.id", ondelete="CASCADE"), nullable=False)
    map_id = Column(UUID(as_uuid=True), ForeignKey("maps.id", ondelete="CASCADE"), nullable=False)
    content = Column(Text, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    map = relationship("Map", back_populates="comments")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "map_id": str(self.map_id),
            "content": self.content,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
