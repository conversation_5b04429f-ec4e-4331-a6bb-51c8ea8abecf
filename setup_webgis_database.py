"""
Setup database untuk ScapeGIS Web GIS
Membuat tabel maps, geo_features, saved_maps, dan comments
"""
import asyncio
import asyncpg
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database connection (dari environment variables)
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:<EMAIL>:5432/postgres")

async def setup_webgis_tables():
    """Setup tabel untuk ScapeGIS Web GIS"""
    
    try:
        # Connect to database
        conn = await asyncpg.connect(DATABASE_URL)
        print("🔗 Connected to database")
        
        # 1. Create maps table
        print("📝 Creating maps table...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS public.maps (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
                title VARCHAR(100) NOT NULL,
                description TEXT,
                base_map_type VARCHAR(20) DEFAULT 'street',
                center_lat FLOAT DEFAULT 0.0,
                center_lng FLOAT DEFAULT 0.0,
                zoom_level INTEGER DEFAULT 2,
                settings JSONB DEFAULT '{}',
                bounds JSONB,
                is_public BOOLEAN DEFAULT FALSE,
                is_featured BOOLEAN DEFAULT FALSE,
                view_count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                feature_count INTEGER DEFAULT 0,
                last_accessed TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        
        # 2. Create geo_features table
        print("📍 Creating geo_features table...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS public.geo_features (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                feature_type VARCHAR(20) NOT NULL,
                geometry JSONB NOT NULL,
                properties JSONB DEFAULT '{}',
                image_url TEXT,
                is_visible BOOLEAN DEFAULT TRUE,
                z_index INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        
        # 3. Create saved_maps table
        print("💾 Creating saved_maps table...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS public.saved_maps (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
                map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
                saved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(user_id, map_id)
            );
        """)
        
        # 4. Create comments table
        print("💬 Creating comments table...")
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS public.comments (
                id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
                map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
                content TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        
        # 5. Create indexes for better performance
        print("🔍 Creating indexes...")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_maps_user_id ON public.maps(user_id);")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_maps_is_public ON public.maps(is_public);")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_maps_created_at ON public.maps(created_at DESC);")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_geo_features_map_id ON public.geo_features(map_id);")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_saved_maps_user_id ON public.saved_maps(user_id);")
        await conn.execute("CREATE INDEX IF NOT EXISTS idx_comments_map_id ON public.comments(map_id);")
        
        # 6. Create trigger function to update feature_count in maps
        print("⚙️ Creating trigger function for feature count...")
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_map_feature_count()
            RETURNS TRIGGER AS $$
            BEGIN
                IF TG_OP = 'INSERT' THEN
                    UPDATE public.maps 
                    SET feature_count = feature_count + 1,
                        updated_at = NOW()
                    WHERE id = NEW.map_id;
                    RETURN NEW;
                ELSIF TG_OP = 'DELETE' THEN
                    UPDATE public.maps 
                    SET feature_count = feature_count - 1,
                        updated_at = NOW()
                    WHERE id = OLD.map_id;
                    RETURN OLD;
                END IF;
                RETURN NULL;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        # 7. Create triggers
        print("🔄 Creating triggers...")
        await conn.execute("""
            DROP TRIGGER IF EXISTS trigger_update_feature_count ON public.geo_features;
            CREATE TRIGGER trigger_update_feature_count
                AFTER INSERT OR DELETE ON public.geo_features
                FOR EACH ROW EXECUTE FUNCTION update_map_feature_count();
        """)
        
        # 8. Create trigger function to update updated_at timestamp
        print("⏰ Creating updated_at trigger function...")
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        """)
        
        # 9. Create updated_at triggers for all tables
        await conn.execute("""
            DROP TRIGGER IF EXISTS trigger_maps_updated_at ON public.maps;
            CREATE TRIGGER trigger_maps_updated_at
                BEFORE UPDATE ON public.maps
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """)
        
        await conn.execute("""
            DROP TRIGGER IF EXISTS trigger_geo_features_updated_at ON public.geo_features;
            CREATE TRIGGER trigger_geo_features_updated_at
                BEFORE UPDATE ON public.geo_features
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """)
        
        await conn.execute("""
            DROP TRIGGER IF EXISTS trigger_comments_updated_at ON public.comments;
            CREATE TRIGGER trigger_comments_updated_at
                BEFORE UPDATE ON public.comments
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        """)
        
        # 10. Set up Row Level Security (RLS) policies
        print("🔒 Setting up Row Level Security policies...")
        
        # Enable RLS on all tables
        await conn.execute("ALTER TABLE public.maps ENABLE ROW LEVEL SECURITY;")
        await conn.execute("ALTER TABLE public.geo_features ENABLE ROW LEVEL SECURITY;")
        await conn.execute("ALTER TABLE public.saved_maps ENABLE ROW LEVEL SECURITY;")
        await conn.execute("ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;")
        
        # Maps policies
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can view their own maps" ON public.maps;
            CREATE POLICY "Users can view their own maps" ON public.maps
                FOR SELECT USING (auth.uid() = user_id);
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can view public maps" ON public.maps;
            CREATE POLICY "Users can view public maps" ON public.maps
                FOR SELECT USING (is_public = true);
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can insert their own maps" ON public.maps;
            CREATE POLICY "Users can insert their own maps" ON public.maps
                FOR INSERT WITH CHECK (auth.uid() = user_id);
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can update their own maps" ON public.maps;
            CREATE POLICY "Users can update their own maps" ON public.maps
                FOR UPDATE USING (auth.uid() = user_id);
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can delete their own maps" ON public.maps;
            CREATE POLICY "Users can delete their own maps" ON public.maps
                FOR DELETE USING (auth.uid() = user_id);
        """)
        
        # Geo features policies
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can view features of accessible maps" ON public.geo_features;
            CREATE POLICY "Users can view features of accessible maps" ON public.geo_features
                FOR SELECT USING (
                    EXISTS (
                        SELECT 1 FROM public.maps 
                        WHERE maps.id = geo_features.map_id 
                        AND (maps.user_id = auth.uid() OR maps.is_public = true)
                    )
                );
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can manage features of their own maps" ON public.geo_features;
            CREATE POLICY "Users can manage features of their own maps" ON public.geo_features
                FOR ALL USING (
                    EXISTS (
                        SELECT 1 FROM public.maps 
                        WHERE maps.id = geo_features.map_id 
                        AND maps.user_id = auth.uid()
                    )
                );
        """)
        
        # Saved maps policies
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can manage their own saved maps" ON public.saved_maps;
            CREATE POLICY "Users can manage their own saved maps" ON public.saved_maps
                FOR ALL USING (auth.uid() = user_id);
        """)
        
        # Comments policies
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can view comments on accessible maps" ON public.comments;
            CREATE POLICY "Users can view comments on accessible maps" ON public.comments
                FOR SELECT USING (
                    EXISTS (
                        SELECT 1 FROM public.maps 
                        WHERE maps.id = comments.map_id 
                        AND (maps.user_id = auth.uid() OR maps.is_public = true)
                    )
                );
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can insert comments on public maps" ON public.comments;
            CREATE POLICY "Users can insert comments on public maps" ON public.comments
                FOR INSERT WITH CHECK (
                    auth.uid() = user_id AND
                    EXISTS (
                        SELECT 1 FROM public.maps 
                        WHERE maps.id = comments.map_id 
                        AND maps.is_public = true
                    )
                );
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can update their own comments" ON public.comments;
            CREATE POLICY "Users can update their own comments" ON public.comments
                FOR UPDATE USING (auth.uid() = user_id);
        """)
        
        await conn.execute("""
            DROP POLICY IF EXISTS "Users can delete their own comments" ON public.comments;
            CREATE POLICY "Users can delete their own comments" ON public.comments
                FOR DELETE USING (auth.uid() = user_id);
        """)
        
        print("✅ Database setup completed successfully!")
        print("\n📊 Created tables:")
        print("   - maps (user-created maps)")
        print("   - geo_features (markers, polygons, etc.)")
        print("   - saved_maps (bookmarked maps)")
        print("   - comments (map comments)")
        print("\n🔒 Row Level Security policies configured")
        print("⚡ Triggers and indexes created")
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        raise
    finally:
        if 'conn' in locals():
            await conn.close()
            print("🔌 Database connection closed")


if __name__ == "__main__":
    print("🚀 Setting up ScapeGIS Web GIS database...")
    asyncio.run(setup_webgis_tables())
