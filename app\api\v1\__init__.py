"""
API v1 module
"""
from .test import router as test_router
from .projects import router as projects_router
from .layers import router as layers_router
from .projects_supabase import router as projects_supabase_router
from .projects_mock import router as projects_mock_router
from .maps import router as maps_router
from .projects_webgis import router as projects_webgis_router
from . import auth

__all__ = ["test_router", "projects_router", "layers_router", "projects_supabase_router", "projects_mock_router", "maps_router", "projects_webgis_router", "auth"]
