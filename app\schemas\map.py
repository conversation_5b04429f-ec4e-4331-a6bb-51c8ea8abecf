"""
Map Schemas for ScapeGIS Web GIS
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime


class MapCenter(BaseModel):
    """Schema for map center coordinates"""
    lat: float = Field(..., ge=-90, le=90, description="Latitude")
    lng: float = Field(..., ge=-180, le=180, description="Longitude")


class CreateMapRequest(BaseModel):
    """Schema for creating a new map"""
    title: str = Field(..., min_length=1, max_length=100, description="Map title")
    description: Optional[str] = Field(None, description="Map description")
    base_map_type: Optional[str] = Field("street", description="Base map type")
    center: Optional[MapCenter] = Field(MapCenter(lat=0.0, lng=0.0), description="Map center")
    zoom_level: Optional[int] = Field(2, ge=0, le=20, description="Map zoom level")
    bounds: Optional[List[List[float]]] = Field(None, description="Map bounds [[sw_lng, sw_lat], [ne_lng, ne_lat]]")
    is_public: Optional[bool] = Field(False, description="Whether map is public")
    settings: Optional[Dict[str, Any]] = Field({}, description="Additional map settings")
    
    @validator('base_map_type')
    def validate_base_map_type(cls, v):
        allowed_types = ['street', 'satellite', 'dark', 'terrain', 'hybrid']
        if v not in allowed_types:
            raise ValueError(f'Base map type must be one of: {", ".join(allowed_types)}')
        return v


class UpdateMapRequest(BaseModel):
    """Schema for updating a map"""
    title: Optional[str] = Field(None, min_length=1, max_length=100, description="Map title")
    description: Optional[str] = Field(None, description="Map description")
    base_map_type: Optional[str] = Field(None, description="Base map type")
    center: Optional[MapCenter] = Field(None, description="Map center")
    zoom_level: Optional[int] = Field(None, ge=0, le=20, description="Map zoom level")
    bounds: Optional[List[List[float]]] = Field(None, description="Map bounds")
    is_public: Optional[bool] = Field(None, description="Whether map is public")
    settings: Optional[Dict[str, Any]] = Field(None, description="Additional map settings")
    
    @validator('base_map_type')
    def validate_base_map_type(cls, v):
        if v is not None:
            allowed_types = ['street', 'satellite', 'dark', 'terrain', 'hybrid']
            if v not in allowed_types:
                raise ValueError(f'Base map type must be one of: {", ".join(allowed_types)}')
        return v


class MapResponse(BaseModel):
    """Schema for map response"""
    id: str
    user_id: str
    title: str
    description: Optional[str]
    base_map_type: str
    center: Dict[str, float]
    zoom_level: int
    settings: Dict[str, Any]
    bounds: Optional[List[List[float]]]
    is_public: bool
    is_featured: bool
    view_count: int
    like_count: int
    feature_count: int
    last_accessed: Optional[str]
    created_at: Optional[str]
    updated_at: Optional[str]


class MapPublicResponse(BaseModel):
    """Schema for public map response (limited info)"""
    id: str
    title: str
    description: Optional[str]
    base_map_type: str
    center: Dict[str, float]
    zoom_level: int
    bounds: Optional[List[List[float]]]
    is_featured: bool
    view_count: int
    like_count: int
    feature_count: int
    created_at: Optional[str]


class CreateGeoFeatureRequest(BaseModel):
    """Schema for creating a geo feature"""
    name: str = Field(..., min_length=1, max_length=100, description="Feature name")
    description: Optional[str] = Field(None, description="Feature description")
    feature_type: str = Field(..., description="Feature type")
    geometry: Dict[str, Any] = Field(..., description="GeoJSON geometry")
    properties: Optional[Dict[str, Any]] = Field({}, description="Feature properties")
    image_url: Optional[str] = Field(None, description="Feature image URL")
    is_visible: Optional[bool] = Field(True, description="Whether feature is visible")
    z_index: Optional[int] = Field(0, description="Layer order")
    
    @validator('feature_type')
    def validate_feature_type(cls, v):
        allowed_types = ['marker', 'polygon', 'line', 'circle', 'point']
        if v not in allowed_types:
            raise ValueError(f'Feature type must be one of: {", ".join(allowed_types)}')
        return v
    
    @validator('geometry')
    def validate_geometry(cls, v):
        # Basic GeoJSON geometry validation
        if not isinstance(v, dict):
            raise ValueError('Geometry must be a valid GeoJSON object')
        if 'type' not in v or 'coordinates' not in v:
            raise ValueError('Geometry must have "type" and "coordinates" properties')
        return v


class UpdateGeoFeatureRequest(BaseModel):
    """Schema for updating a geo feature"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Feature name")
    description: Optional[str] = Field(None, description="Feature description")
    feature_type: Optional[str] = Field(None, description="Feature type")
    geometry: Optional[Dict[str, Any]] = Field(None, description="GeoJSON geometry")
    properties: Optional[Dict[str, Any]] = Field(None, description="Feature properties")
    image_url: Optional[str] = Field(None, description="Feature image URL")
    is_visible: Optional[bool] = Field(None, description="Whether feature is visible")
    z_index: Optional[int] = Field(None, description="Layer order")
    
    @validator('feature_type')
    def validate_feature_type(cls, v):
        if v is not None:
            allowed_types = ['marker', 'polygon', 'line', 'circle', 'point']
            if v not in allowed_types:
                raise ValueError(f'Feature type must be one of: {", ".join(allowed_types)}')
        return v


class GeoFeatureResponse(BaseModel):
    """Schema for geo feature response"""
    id: str
    map_id: str
    name: str
    description: Optional[str]
    feature_type: str
    geometry: Dict[str, Any]
    properties: Dict[str, Any]
    image_url: Optional[str]
    is_visible: bool
    z_index: int
    created_at: Optional[str]
    updated_at: Optional[str]


class CreateCommentRequest(BaseModel):
    """Schema for creating a comment"""
    content: str = Field(..., min_length=1, max_length=1000, description="Comment content")


class CommentResponse(BaseModel):
    """Schema for comment response"""
    id: str
    user_id: str
    map_id: str
    content: str
    created_at: Optional[str]
    updated_at: Optional[str]


class SavedMapResponse(BaseModel):
    """Schema for saved map response"""
    id: str
    user_id: str
    map_id: str
    saved_at: Optional[str]


class MapListResponse(BaseModel):
    """Schema for map list response with pagination"""
    maps: List[MapResponse]
    total: int
    page: int
    per_page: int
    total_pages: int


class PublicMapListResponse(BaseModel):
    """Schema for public map list response with pagination"""
    maps: List[MapPublicResponse]
    total: int
    page: int
    per_page: int
    total_pages: int
