"""
Projects API endpoints for ScapeGIS Web GIS (Enhanced version)
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from supabase import Client
from app.core.database import get_supabase
from app.core.auth import get_current_user
from app.schemas.project import (
    CreateProjectRequest, UpdateProjectRequest, ProjectResponse,
    DuplicateProjectRequest
)
import logging
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/projects-webgis", tags=["projects-webgis"])


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: CreateProjectRequest,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Create a new project"""
    try:
        # Prepare project data
        new_project = {
            "id": str(uuid.uuid4()),
            "owner_id": current_user["id"],
            "name": project_data.name,
            "description": project_data.description,
            "workspace_id": project_data.workspace_id,
            "settings": {
                "mapCenter": project_data.mapCenter,
                "mapZoom": project_data.mapZoom,
                "bounds": project_data.bounds
            },
            "zoom_level": project_data.mapZoom,
            "is_public": project_data.is_public,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        # Insert into Supabase
        result = supabase.table("projects").insert(new_project).execute()
        
        if result.data:
            created_project = result.data[0]
            return ProjectResponse(
                id=created_project["id"],
                name=created_project["name"],
                description=created_project.get("description"),
                workspace_id=created_project.get("workspace_id"),
                owner_id=created_project["owner_id"],
                settings=created_project.get("settings", {}),
                mapCenter=created_project.get("settings", {}).get("mapCenter", [0, 0]),
                mapZoom=created_project.get("settings", {}).get("mapZoom", 2),
                bounds=created_project.get("settings", {}).get("bounds"),
                zoom_level=created_project["zoom_level"],
                is_public=created_project["is_public"],
                layer_count=created_project.get("layer_count", 0),
                last_accessed=created_project.get("last_accessed"),
                created_at=created_project.get("created_at"),
                updated_at=created_project.get("updated_at"),
                layers=[]
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create project"
            )
            
    except Exception as e:
        logger.error(f"Error creating project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create project: {str(e)}"
        )


@router.get("/", response_model=List[ProjectResponse])
async def get_user_projects(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Get user's projects with pagination"""
    try:
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Get user's projects
        result = supabase.table("projects")\
            .select("*")\
            .eq("owner_id", current_user["id"])\
            .order("created_at", desc=True)\
            .range(offset, offset + per_page - 1)\
            .execute()
        
        projects = []
        if result.data:
            for project_data in result.data:
                projects.append(ProjectResponse(
                    id=project_data["id"],
                    name=project_data["name"],
                    description=project_data.get("description"),
                    workspace_id=project_data.get("workspace_id"),
                    owner_id=project_data["owner_id"],
                    settings=project_data.get("settings", {}),
                    mapCenter=project_data.get("settings", {}).get("mapCenter", [0, 0]),
                    mapZoom=project_data.get("settings", {}).get("mapZoom", 2),
                    bounds=project_data.get("settings", {}).get("bounds"),
                    zoom_level=project_data["zoom_level"],
                    is_public=project_data["is_public"],
                    layer_count=project_data.get("layer_count", 0),
                    last_accessed=project_data.get("last_accessed"),
                    created_at=project_data.get("created_at"),
                    updated_at=project_data.get("updated_at"),
                    layers=[]
                ))
        
        return projects
        
    except Exception as e:
        logger.error(f"Error getting user projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get projects: {str(e)}"
        )


@router.get("/public", response_model=List[ProjectResponse])
async def get_public_projects(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    supabase: Client = Depends(get_supabase)
):
    """Get public projects with pagination"""
    try:
        # Calculate offset
        offset = (page - 1) * per_page
        
        # Get public projects
        result = supabase.table("projects")\
            .select("*")\
            .eq("is_public", True)\
            .order("created_at", desc=True)\
            .range(offset, offset + per_page - 1)\
            .execute()
        
        projects = []
        if result.data:
            for project_data in result.data:
                projects.append(ProjectResponse(
                    id=project_data["id"],
                    name=project_data["name"],
                    description=project_data.get("description"),
                    workspace_id=project_data.get("workspace_id"),
                    owner_id=project_data["owner_id"],
                    settings=project_data.get("settings", {}),
                    mapCenter=project_data.get("settings", {}).get("mapCenter", [0, 0]),
                    mapZoom=project_data.get("settings", {}).get("mapZoom", 2),
                    bounds=project_data.get("settings", {}).get("bounds"),
                    zoom_level=project_data["zoom_level"],
                    is_public=project_data["is_public"],
                    layer_count=project_data.get("layer_count", 0),
                    last_accessed=project_data.get("last_accessed"),
                    created_at=project_data.get("created_at"),
                    updated_at=project_data.get("updated_at"),
                    layers=[]
                ))
        
        return projects
        
    except Exception as e:
        logger.error(f"Error getting public projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get public projects: {str(e)}"
        )


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: str,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Get a specific project by ID"""
    try:
        # Get project
        result = supabase.table("projects")\
            .select("*")\
            .eq("id", project_id)\
            .execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        
        project_data = result.data[0]
        
        # Check if user has access (owner or public project)
        if project_data["owner_id"] != current_user["id"] and not project_data["is_public"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get project layers
        layers_result = supabase.table("layers")\
            .select("*")\
            .eq("project_id", project_id)\
            .order("z_index")\
            .execute()
        
        layers = layers_result.data if layers_result.data else []
        
        return ProjectResponse(
            id=project_data["id"],
            name=project_data["name"],
            description=project_data.get("description"),
            workspace_id=project_data.get("workspace_id"),
            owner_id=project_data["owner_id"],
            settings=project_data.get("settings", {}),
            mapCenter=project_data.get("settings", {}).get("mapCenter", [0, 0]),
            mapZoom=project_data.get("settings", {}).get("mapZoom", 2),
            bounds=project_data.get("settings", {}).get("bounds"),
            zoom_level=project_data["zoom_level"],
            is_public=project_data["is_public"],
            layer_count=project_data.get("layer_count", 0),
            last_accessed=project_data.get("last_accessed"),
            created_at=project_data.get("created_at"),
            updated_at=project_data.get("updated_at"),
            layers=layers
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get project: {str(e)}"
        )


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: str,
    project_data: UpdateProjectRequest,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Update a project"""
    try:
        # Check if project exists and user owns it
        result = supabase.table("projects")\
            .select("*")\
            .eq("id", project_id)\
            .eq("owner_id", current_user["id"])\
            .execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or access denied"
            )
        
        existing_project = result.data[0]
        existing_settings = existing_project.get("settings", {})
        
        # Prepare update data
        update_data = {"updated_at": datetime.utcnow().isoformat()}
        
        if project_data.name is not None:
            update_data["name"] = project_data.name
        if project_data.description is not None:
            update_data["description"] = project_data.description
        if project_data.workspace_id is not None:
            update_data["workspace_id"] = project_data.workspace_id
        if project_data.is_public is not None:
            update_data["is_public"] = project_data.is_public
        
        # Update settings
        new_settings = existing_settings.copy()
        if project_data.mapCenter is not None:
            new_settings["mapCenter"] = project_data.mapCenter
        if project_data.mapZoom is not None:
            new_settings["mapZoom"] = project_data.mapZoom
            update_data["zoom_level"] = project_data.mapZoom
        if project_data.bounds is not None:
            new_settings["bounds"] = project_data.bounds
        
        update_data["settings"] = new_settings
        
        # Update project
        result = supabase.table("projects")\
            .update(update_data)\
            .eq("id", project_id)\
            .execute()
        
        if result.data:
            updated_project = result.data[0]
            return ProjectResponse(
                id=updated_project["id"],
                name=updated_project["name"],
                description=updated_project.get("description"),
                workspace_id=updated_project.get("workspace_id"),
                owner_id=updated_project["owner_id"],
                settings=updated_project.get("settings", {}),
                mapCenter=updated_project.get("settings", {}).get("mapCenter", [0, 0]),
                mapZoom=updated_project.get("settings", {}).get("mapZoom", 2),
                bounds=updated_project.get("settings", {}).get("bounds"),
                zoom_level=updated_project["zoom_level"],
                is_public=updated_project["is_public"],
                layer_count=updated_project.get("layer_count", 0),
                last_accessed=updated_project.get("last_accessed"),
                created_at=updated_project.get("created_at"),
                updated_at=updated_project.get("updated_at"),
                layers=[]
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update project"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update project: {str(e)}"
        )


@router.delete("/{project_id}")
async def delete_project(
    project_id: str,
    current_user: dict = Depends(get_current_user),
    supabase: Client = Depends(get_supabase)
):
    """Delete a project"""
    try:
        # Check if project exists and user owns it
        result = supabase.table("projects")\
            .select("id")\
            .eq("id", project_id)\
            .eq("owner_id", current_user["id"])\
            .execute()
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or access denied"
            )
        
        # Delete project (cascade will delete related layers)
        result = supabase.table("projects")\
            .delete()\
            .eq("id", project_id)\
            .execute()
        
        return {"message": "Project deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete project: {str(e)}"
        )
