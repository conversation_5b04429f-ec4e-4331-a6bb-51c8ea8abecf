-- ScapeGIS Web GIS Database Schema - Safe Migration
-- Run this SQL in Supabase SQL Editor
-- This script safely handles existing user_profiles table

-- 1. First, let's check and add missing columns to user_profiles if they don't exist
DO $$
BEGIN
    -- Add username column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'username') THEN
        ALTER TABLE public.user_profiles ADD COLUMN username VARCHAR(50) UNIQUE;
    END IF;
    
    -- Add full_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'full_name') THEN
        ALTER TABLE public.user_profiles ADD COLUMN full_name VARCHAR(100);
    END IF;
    
    -- Add avatar_url column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'avatar_url') THEN
        ALTER TABLE public.user_profiles ADD COLUMN avatar_url TEXT;
    END IF;
    
    -- Add workspace_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'workspace_id') THEN
        ALTER TABLE public.user_profiles ADD COLUMN workspace_id UUID;
    END IF;
    
    -- Add is_verified column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'is_verified') THEN
        ALTER TABLE public.user_profiles ADD COLUMN is_verified BOOLEAN DEFAULT FALSE;
    END IF;
    
    -- Add verification_token column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'verification_token') THEN
        ALTER TABLE public.user_profiles ADD COLUMN verification_token VARCHAR(255) UNIQUE;
    END IF;
    
    -- Add verification_token_expires column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'verification_token_expires') THEN
        ALTER TABLE public.user_profiles ADD COLUMN verification_token_expires TIMESTAMP WITH TIME ZONE;
    END IF;
    
    -- Add reset_password_token column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'reset_password_token') THEN
        ALTER TABLE public.user_profiles ADD COLUMN reset_password_token VARCHAR(255) UNIQUE;
    END IF;
    
    -- Add reset_password_expires column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'reset_password_expires') THEN
        ALTER TABLE public.user_profiles ADD COLUMN reset_password_expires TIMESTAMP WITH TIME ZONE;
    END IF;
    
    -- Add last_login column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'last_login') THEN
        ALTER TABLE public.user_profiles ADD COLUMN last_login TIMESTAMP WITH TIME ZONE;
    END IF;
    
    -- Add login_count column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'login_count') THEN
        ALTER TABLE public.user_profiles ADD COLUMN login_count INTEGER DEFAULT 0;
    END IF;
    
    -- Add provider column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'provider') THEN
        ALTER TABLE public.user_profiles ADD COLUMN provider VARCHAR(20) DEFAULT 'email';
    END IF;
    
    -- Add provider_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'provider_id') THEN
        ALTER TABLE public.user_profiles ADD COLUMN provider_id VARCHAR(255);
    END IF;
    
    -- Add created_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'created_at') THEN
        ALTER TABLE public.user_profiles ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
    
    -- Add updated_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'updated_at') THEN
        ALTER TABLE public.user_profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- 2. Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    workspace_id UUID,
    owner_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
    settings JSONB DEFAULT '{}',
    zoom_level INTEGER DEFAULT 2,
    is_public BOOLEAN DEFAULT FALSE,
    layer_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create layers table
CREATE TABLE IF NOT EXISTS public.layers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    layer_type VARCHAR(20) NOT NULL,
    data_source TEXT,
    style_config JSONB DEFAULT '{}',
    is_visible BOOLEAN DEFAULT TRUE,
    opacity FLOAT DEFAULT 1.0,
    z_index INTEGER DEFAULT 0,
    feature_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create maps table
CREATE TABLE IF NOT EXISTS public.maps (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    base_map_type VARCHAR(20) DEFAULT 'street',
    center_lat FLOAT DEFAULT 0.0,
    center_lng FLOAT DEFAULT 0.0,
    zoom_level INTEGER DEFAULT 2,
    settings JSONB DEFAULT '{}',
    bounds JSONB,
    is_public BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    feature_count INTEGER DEFAULT 0,
    last_accessed TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create geo_features table
CREATE TABLE IF NOT EXISTS public.geo_features (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    feature_type VARCHAR(20) NOT NULL,
    geometry JSONB NOT NULL,
    properties JSONB DEFAULT '{}',
    image_url TEXT,
    is_visible BOOLEAN DEFAULT TRUE,
    z_index INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create saved_maps table
CREATE TABLE IF NOT EXISTS public.saved_maps (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
    map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
    saved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, map_id)
);

-- 7. Create comments table
CREATE TABLE IF NOT EXISTS public.comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE NOT NULL,
    map_id UUID REFERENCES public.maps(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Create indexes (with safe column checks)
-- User profiles indexes - only create if columns exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'username') THEN
        CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON public.user_profiles(username);
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_profiles' AND column_name = 'provider') THEN
        CREATE INDEX IF NOT EXISTS idx_user_profiles_provider ON public.user_profiles(provider);
    END IF;
END $$;

-- Projects indexes
CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON public.projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_projects_is_public ON public.projects(is_public);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON public.projects(created_at DESC);

-- Layers indexes
CREATE INDEX IF NOT EXISTS idx_layers_project_id ON public.layers(project_id);
CREATE INDEX IF NOT EXISTS idx_layers_layer_type ON public.layers(layer_type);

-- Maps indexes
CREATE INDEX IF NOT EXISTS idx_maps_user_id ON public.maps(user_id);
CREATE INDEX IF NOT EXISTS idx_maps_is_public ON public.maps(is_public);
CREATE INDEX IF NOT EXISTS idx_maps_created_at ON public.maps(created_at DESC);

-- Geo features indexes
CREATE INDEX IF NOT EXISTS idx_geo_features_map_id ON public.geo_features(map_id);
CREATE INDEX IF NOT EXISTS idx_geo_features_feature_type ON public.geo_features(feature_type);

-- Saved maps indexes
CREATE INDEX IF NOT EXISTS idx_saved_maps_user_id ON public.saved_maps(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_maps_map_id ON public.saved_maps(map_id);

-- Comments indexes
CREATE INDEX IF NOT EXISTS idx_comments_map_id ON public.comments(map_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);

-- 9. Create trigger function to update layer_count in projects
CREATE OR REPLACE FUNCTION update_project_layer_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.projects
        SET layer_count = layer_count + 1,
            updated_at = NOW()
        WHERE id = NEW.project_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.projects
        SET layer_count = layer_count - 1,
            updated_at = NOW()
        WHERE id = OLD.project_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 10. Create trigger function to update feature_count in maps
CREATE OR REPLACE FUNCTION update_map_feature_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.maps 
        SET feature_count = feature_count + 1,
            updated_at = NOW()
        WHERE id = NEW.map_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.maps 
        SET feature_count = feature_count - 1,
            updated_at = NOW()
        WHERE id = OLD.map_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 11. Create trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. Create triggers for counting
DROP TRIGGER IF EXISTS trigger_update_layer_count ON public.layers;
CREATE TRIGGER trigger_update_layer_count
    AFTER INSERT OR DELETE ON public.layers
    FOR EACH ROW EXECUTE FUNCTION update_project_layer_count();

DROP TRIGGER IF EXISTS trigger_update_feature_count ON public.geo_features;
CREATE TRIGGER trigger_update_feature_count
    AFTER INSERT OR DELETE ON public.geo_features
    FOR EACH ROW EXECUTE FUNCTION update_map_feature_count();

-- 13. Create updated_at triggers for all tables
DROP TRIGGER IF EXISTS trigger_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER trigger_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_projects_updated_at ON public.projects;
CREATE TRIGGER trigger_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_layers_updated_at ON public.layers;
CREATE TRIGGER trigger_layers_updated_at
    BEFORE UPDATE ON public.layers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_maps_updated_at ON public.maps;
CREATE TRIGGER trigger_maps_updated_at
    BEFORE UPDATE ON public.maps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_geo_features_updated_at ON public.geo_features;
CREATE TRIGGER trigger_geo_features_updated_at
    BEFORE UPDATE ON public.geo_features
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_comments_updated_at ON public.comments;
CREATE TRIGGER trigger_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 14. Enable Row Level Security (RLS) on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.layers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.maps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.geo_features ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.saved_maps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- 15. Create RLS policies for user_profiles table
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- 16. Create RLS policies for projects table
DROP POLICY IF EXISTS "Users can view their own projects" ON public.projects;
CREATE POLICY "Users can view their own projects" ON public.projects
    FOR SELECT USING (auth.uid() = owner_id);

DROP POLICY IF EXISTS "Users can view public projects" ON public.projects;
CREATE POLICY "Users can view public projects" ON public.projects
    FOR SELECT USING (is_public = true);

DROP POLICY IF EXISTS "Users can insert their own projects" ON public.projects;
CREATE POLICY "Users can insert their own projects" ON public.projects
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

DROP POLICY IF EXISTS "Users can update their own projects" ON public.projects;
CREATE POLICY "Users can update their own projects" ON public.projects
    FOR UPDATE USING (auth.uid() = owner_id);

DROP POLICY IF EXISTS "Users can delete their own projects" ON public.projects;
CREATE POLICY "Users can delete their own projects" ON public.projects
    FOR DELETE USING (auth.uid() = owner_id);

-- 17. Create RLS policies for layers table
DROP POLICY IF EXISTS "Users can view layers of accessible projects" ON public.layers;
CREATE POLICY "Users can view layers of accessible projects" ON public.layers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects
            WHERE projects.id = layers.project_id
            AND (projects.owner_id = auth.uid() OR projects.is_public = true)
        )
    );

DROP POLICY IF EXISTS "Users can manage layers of their own projects" ON public.layers;
CREATE POLICY "Users can manage layers of their own projects" ON public.layers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects
            WHERE projects.id = layers.project_id
            AND projects.owner_id = auth.uid()
        )
    );

-- 18. Create RLS policies for maps table
DROP POLICY IF EXISTS "Users can view their own maps" ON public.maps;
CREATE POLICY "Users can view their own maps" ON public.maps
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view public maps" ON public.maps;
CREATE POLICY "Users can view public maps" ON public.maps
    FOR SELECT USING (is_public = true);

DROP POLICY IF EXISTS "Users can insert their own maps" ON public.maps;
CREATE POLICY "Users can insert their own maps" ON public.maps
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own maps" ON public.maps;
CREATE POLICY "Users can update their own maps" ON public.maps
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own maps" ON public.maps;
CREATE POLICY "Users can delete their own maps" ON public.maps
    FOR DELETE USING (auth.uid() = user_id);

-- 19. Create RLS policies for geo_features table
DROP POLICY IF EXISTS "Users can view features of accessible maps" ON public.geo_features;
CREATE POLICY "Users can view features of accessible maps" ON public.geo_features
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.maps 
            WHERE maps.id = geo_features.map_id 
            AND (maps.user_id = auth.uid() OR maps.is_public = true)
        )
    );

DROP POLICY IF EXISTS "Users can manage features of their own maps" ON public.geo_features;
CREATE POLICY "Users can manage features of their own maps" ON public.geo_features
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.maps 
            WHERE maps.id = geo_features.map_id 
            AND maps.user_id = auth.uid()
        )
    );

-- 20. Create RLS policies for saved_maps table
DROP POLICY IF EXISTS "Users can manage their own saved maps" ON public.saved_maps;
CREATE POLICY "Users can manage their own saved maps" ON public.saved_maps
    FOR ALL USING (auth.uid() = user_id);

-- 21. Create RLS policies for comments table
DROP POLICY IF EXISTS "Users can view comments on accessible maps" ON public.comments;
CREATE POLICY "Users can view comments on accessible maps" ON public.comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.maps 
            WHERE maps.id = comments.map_id 
            AND (maps.user_id = auth.uid() OR maps.is_public = true)
        )
    );

DROP POLICY IF EXISTS "Users can insert comments on public maps" ON public.comments;
CREATE POLICY "Users can insert comments on public maps" ON public.comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.maps 
            WHERE maps.id = comments.map_id 
            AND maps.is_public = true
        )
    );

DROP POLICY IF EXISTS "Users can update their own comments" ON public.comments;
CREATE POLICY "Users can update their own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own comments" ON public.comments;
CREATE POLICY "Users can delete their own comments" ON public.comments
    FOR DELETE USING (auth.uid() = user_id);

-- 22. Show completion message
SELECT 'Database schema migration completed successfully!' as status;